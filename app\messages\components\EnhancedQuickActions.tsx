"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  MessageSquare,
  Package,
  Utensils,
  Truck,
  Clock,
  Star,
  ArrowRight,
  Zap,
  AlertCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { MESSAGE_TEMPLATES, QUICK_RESPONSES, MessageTemplate, QuickResponse } from '@/types/message-templates'
import { ReviewSelectionModal } from './ReviewSelectionModal'
import { ReviewResponseComposer } from './ReviewResponseComposer'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Context {
  order_id?: string | null
  business_id?: string | null
  rider_id?: string | null
  role?: 'customer' | 'business' | 'rider' | null
}

interface EnhancedQuickAction {
  id: string
  type: string // Can be 'template', 'quick_response', 'custom', 'review_response', etc.
  title: string
  description?: string
  content?: string
  icon: React.ReactNode | string // Can be React component or emoji string
  priority?: number
  is_urgent?: boolean
  urgent?: boolean // API uses 'urgent' field
  template?: MessageTemplate
  quick_response?: QuickResponse
  context?: any
  actionData?: any // API response includes actionData
  color?: string // API includes color
  category?: string // API includes category
}

interface EnhancedQuickActionsProps {
  user: User
  context?: Context
  onTemplateSelected: (template: MessageTemplate, context?: any) => void
  onQuickResponseSelected: (response: QuickResponse) => void
  onCustomActionSelected: (action: any) => void
}

export function EnhancedQuickActions({
  user,
  context,
  onTemplateSelected,
  onQuickResponseSelected,
  onCustomActionSelected
}: EnhancedQuickActionsProps) {
  const [actions, setActions] = useState<EnhancedQuickAction[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [userRole, setUserRole] = useState<'customer' | 'business' | 'driver'>('customer')
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewType, setReviewType] = useState<'order' | 'business' | 'driver'>('order')
  const [preSelectedOrder, setPreSelectedOrder] = useState<any>(null)
  const [showReviewResponseComposer, setShowReviewResponseComposer] = useState(false)
  const [selectedBusinessId, setSelectedBusinessId] = useState<string>('')
  const [selectedBusinessName, setSelectedBusinessName] = useState<string>('')

  useEffect(() => {
    generateContextualActions()
  }, [user, context])

  const generateContextualActions = async () => {
    setIsLoading(true)

    try {
      // Get enhanced quick actions from API
      const enhancedActionsData = await fetchEnhancedQuickActions(user.id)
      const role = enhancedActionsData.user_capabilities?.primaryRole || 'customer'
      setUserRole(role)

      // Convert API actions to component actions
      const apiActions: EnhancedQuickAction[] = (enhancedActionsData.quick_actions || []).map((action: any) => {
        console.log('🔄 Processing API action:', action)
        return {
          id: action.id,
          type: action.type,
          title: action.title,
          description: action.description,
          icon: action.icon || <MessageSquare className="h-5 w-5" />,
          priority: action.priority || 10,
          is_urgent: action.urgent || false,
          context: action.context,
          actionData: action.actionData
        }
      })

      console.log('📋 API Actions processed:', apiActions)

      // Get contextual data from API for template actions
      const contextData = await fetchContextualData(user.id, context)

      // Get contextual templates based on user role and context
      const contextualTemplates = getContextualTemplates(role, context, contextData)

      // Convert templates to actions
      const templateActions: EnhancedQuickAction[] = contextualTemplates.map(template => ({
        id: template.id,
        type: 'template',
        title: template.title,
        description: getTemplateDescription(template, context, contextData),
        content: template.content,
        icon: getTemplateIcon(template),
        priority: template.priority,
        is_urgent: template.is_urgent,
        template
      }))

      // Add custom contextual actions based on real data
      const customActions = await getCustomActions(user, context, role, contextData)

      // Combine API actions, template actions, and custom actions
      const contextualActions = [...apiActions, ...templateActions, ...customActions]

      // Add default actions if we don't have enough contextual actions
      const defaultActions = contextualActions.length < 4 ? getDefaultActions(role) : []

      // Combine and sort by priority
      const allActions = [...contextualActions, ...defaultActions]
        .sort((a, b) => a.priority - b.priority)
        .slice(0, 8) // Limit to top 8 actions for better coverage

      setActions(allActions)
    } catch (error) {
      console.error('Error generating contextual actions:', error)
      // Fallback to basic actions
      setActions(getDefaultActions(userRole))
    } finally {
      setIsLoading(false)
    }
  }

  const getContextualTemplates = (role: 'customer' | 'business' | 'driver', context?: Context, contextData?: any): MessageTemplate[] => {
    return MESSAGE_TEMPLATES.filter(template => {
      // Check if user role can use this template
      if (!template.user_roles.includes(role)) return false

      // Special logic for review templates - show them if user has recent completed orders or business interactions
      if (template.id === 'order-review' && role === 'customer') {
        return contextData?.has_unreviewed_orders
      }
      if (template.id === 'business-review' && role === 'customer') {
        return contextData?.has_unreviewed_orders
      }
      if (template.id === 'driver-review' && role === 'customer') {
        return contextData?.has_unreviewed_orders
      }
      if (template.id === 'quick-positive-review' && role === 'customer') {
        return contextData?.has_unreviewed_orders
      }

      // Enhanced review-message integration templates
      if (template.id === 'review-discussion' && role === 'customer') {
        return contextData?.has_recent_completed_orders
      }
      if (template.id === 'review-follow-up' && role === 'customer') {
        return contextData?.has_recent_completed_orders
      }
      if (template.id === 'review-question' && role === 'customer') {
        return true // Always show this for customers
      }

      // Check context requirements for other templates
      if (template.context_required) {
        if (template.context_required.order_id && !context?.order_id && !contextData?.has_active_orders) return false
        if (template.context_required.business_id && !context?.business_id && !contextData?.has_recent_business_interactions) return false
        if (template.context_required.driver_id && !context?.rider_id) return false
      }

      // Prioritize templates based on current context
      if (contextData?.has_active_orders && template.category === 'order') {
        template.priority = Math.max(1, template.priority - 1) // Boost priority
      }

      return true
    }).sort((a, b) => a.priority - b.priority)
  }

  const getTemplateIcon = (template: MessageTemplate): React.ReactNode => {
    switch (template.category) {
      case 'order':
        return <Package className="h-5 w-5 text-orange-600" />
      case 'delivery':
        return <Truck className="h-5 w-5 text-blue-600" />
      case 'business':
        return <Utensils className="h-5 w-5 text-green-600" />
      case 'customer_service':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      default:
        return <MessageSquare className="h-5 w-5 text-emerald-600" />
    }
  }

  const getTemplateDescription = (template: MessageTemplate, context?: Context, contextData?: any): string => {
    // Special descriptions for review templates
    if (template.id === 'order-review' && contextData?.unreviewed_orders?.[0]) {
      const order = contextData.unreviewed_orders[0]
      const businessName = order.businesses?.display_name || order.businesses?.name
      return `Review order #${order.order_number} from ${businessName}`
    }

    if (template.id === 'business-review' && contextData?.unreviewed_orders?.[0]) {
      const order = contextData.unreviewed_orders[0]
      const businessName = order.businesses?.display_name || order.businesses?.name
      return `Share your experience with ${businessName}`
    }

    if (template.id === 'driver-review' && contextData?.unreviewed_orders?.[0]) {
      const order = contextData.unreviewed_orders[0]
      return `Rate your delivery experience for order #${order.order_number}`
    }

    if (template.id === 'review-discussion' && contextData?.recent_completed_orders?.[0]) {
      const order = contextData.recent_completed_orders[0]
      const businessName = order.businesses?.display_name || order.businesses?.name
      return `Start a discussion about ${businessName}`
    }

    if (template.id === 'review-follow-up' && contextData?.recent_completed_orders?.[0]) {
      const order = contextData.recent_completed_orders[0]
      const businessName = order.businesses?.display_name || order.businesses?.name
      return `Follow up on your ${businessName} experience`
    }

    if (template.id === 'quick-positive-review') {
      if (contextData?.recent_completed_orders?.[0]) {
        const order = contextData.recent_completed_orders[0]
        const businessName = order.businesses?.display_name || order.businesses?.name
        return `Quick thank you to ${businessName}`
      }
      return 'Send a quick positive review'
    }

    if (template.variables && (context || contextData)) {
      // Try to populate some variables for preview
      let description = template.content

      // Use context data for more accurate previews
      if (contextData?.order?.order_number && template.variables.includes('order_number')) {
        description = description.replace('{order_number}', `#${contextData.order.order_number}`)
      } else if (context?.order_id && template.variables.includes('order_number')) {
        description = description.replace('{order_number}', `#${context.order_id.slice(-6)}`)
      }

      if (contextData?.business?.name && template.variables.includes('business_name')) {
        description = description.replace('{business_name}', contextData.business.name)
      }

      return description.length > 60 ? description.substring(0, 60) + '...' : description
    }
    return template.content.length > 60 ? template.content.substring(0, 60) + '...' : template.content
  }

  const getCustomActions = async (user: User, context?: Context, role?: string, contextData?: any): Promise<EnhancedQuickAction[]> => {
    const customActions: EnhancedQuickAction[] = []

    // Add review quick actions for customers with recent completed orders
    if (role === 'customer' && contextData?.recent_completed_orders?.length > 0) {
      const recentOrders = contextData.recent_completed_orders.slice(0, 3) // Show top 3 recent orders

      recentOrders.forEach((order: any, index: number) => {
        const businessName = order.businesses?.display_name || order.businesses?.name || 'Business'

        // Add business review quick action
        customActions.push({
          id: `quick-review-business-${order.id}`,
          type: 'custom',
          title: `Review ${businessName}`,
          description: `Rate order #${order.order_number}`,
          icon: <Star className="h-5 w-5 text-yellow-500" />,
          priority: 1 + index,
          context: {
            type: 'quick_review',
            review_type: 'business',
            order: order,
            business_name: businessName
          }
        })

        // Add driver review quick action if driver was assigned
        if (order.driver_id) {
          customActions.push({
            id: `quick-review-driver-${order.id}`,
            type: 'custom',
            title: `Review Driver`,
            description: `Rate delivery for order #${order.order_number}`,
            icon: <Truck className="h-5 w-5 text-blue-500" />,
            priority: 1 + index + 0.5,
            context: {
              type: 'quick_review',
              review_type: 'driver',
              order: order,
              business_name: businessName
            }
          })
        }
      })
    }

    // Check for active orders using real data
    if (role === 'customer' && contextData?.has_active_orders) {
      customActions.push({
        id: 'check-active-orders',
        type: 'custom',
        title: 'Check my active orders',
        description: `You have ${contextData.active_orders?.length || 0} active order(s)`,
        icon: <Clock className="h-5 w-5 text-orange-600" />,
        priority: 10,
        context: { type: 'active_orders', orders: contextData.active_orders }
      })
    }

    // Add role-specific actions
    if (role === 'business') {
      customActions.push({
        id: 'coordinate-delivery',
        type: 'custom',
        title: 'Coordinate with drivers',
        description: 'Manage delivery logistics',
        icon: <Truck className="h-5 w-5 text-blue-600" />,
        priority: 2,
        context: { type: 'driver_coordination' }
      })
    }

    // Add recent conversation continuation if available
    if (contextData?.recent_conversations) {
      customActions.push({
        id: 'continue-recent',
        type: 'custom',
        title: 'Continue recent conversation',
        description: 'Pick up where you left off',
        icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
        priority: 15,
        context: { type: 'recent_conversation' }
      })
    }

    return customActions
  }

  const getDefaultActions = (role: 'customer' | 'business' | 'driver'): EnhancedQuickAction[] => {
    const defaultActions: EnhancedQuickAction[] = []

    if (role === 'customer') {
      // Add standard customer quick actions
      defaultActions.push(
        {
          id: 'ask-order-status',
          type: 'template',
          title: 'Ask about order status',
          description: 'Get updates on your order',
          icon: <Package className="h-5 w-5 text-blue-600" />,
          priority: 20,
          template: MESSAGE_TEMPLATES.find(t => t.id === 'order-status-inquiry')!
        },
        {
          id: 'request-refund',
          type: 'template',
          title: 'Request refund',
          description: 'Request a refund for your order',
          icon: <AlertCircle className="h-5 w-5 text-red-600" />,
          priority: 21,
          template: MESSAGE_TEMPLATES.find(t => t.id === 'refund-request')!
        },
        {
          id: 'leave-driver-review',
          type: 'template',
          title: 'Leave driver review',
          description: 'Rate your delivery experience',
          icon: <Star className="h-5 w-5 text-yellow-600" />,
          priority: 22,
          template: MESSAGE_TEMPLATES.find(t => t.id === 'driver-review')!
        },
        {
          id: 'thank-you-message',
          type: 'template',
          title: 'Thank you message',
          description: 'Send appreciation',
          icon: <MessageSquare className="h-5 w-5 text-green-600" />,
          priority: 23,
          template: MESSAGE_TEMPLATES.find(t => t.id === 'thank-you')!
        }
      )
    }

    // Add general networking option for all roles
    defaultActions.push({
      id: 'general-message',
      type: 'custom',
      title: 'Start a conversation',
      description: 'Connect with someone new',
      icon: <MessageSquare className="h-5 w-5 text-emerald-600" />,
      priority: 30,
      context: { type: 'general' }
    })

    return defaultActions
  }

  // API functions for real data
  const fetchEnhancedQuickActions = async (authId: string) => {
    try {
      console.log('🔄 Fetching enhanced quick actions for user:', authId)

      const url = `/api/messages/enhanced-quick-actions?auth_id=${authId}`
      console.log('🌐 Enhanced Actions API URL:', url)

      const response = await fetch(url)
      const data = await response.json()

      console.log('📊 Enhanced Actions API Response:', data)

      return data
    } catch (error) {
      console.error('❌ Error fetching enhanced quick actions:', error)
      return {
        success: false,
        quick_actions: [],
        user_capabilities: { primaryRole: 'customer' }
      }
    }
  }

  const fetchContextualData = async (userId: string, context?: Context) => {
    try {
      console.log('🔄 Fetching contextual data for user:', userId, 'with context:', context)

      const params = new URLSearchParams({ user_id: userId })
      if (context?.order_id) params.append('order_id', context.order_id)
      if (context?.business_id) params.append('business_id', context.business_id)
      if (context?.rider_id) params.append('rider_id', context.rider_id)

      const url = `/api/messages/context?${params}`
      console.log('🌐 API URL:', url)

      const response = await fetch(url)
      const data = await response.json()

      console.log('📊 API Response:', data)

      if (data.success) {
        console.log('✅ Context data loaded:', data.context)
        return data.context
      }
      throw new Error(data.error || 'Failed to fetch context')
    } catch (error) {
      console.error('❌ Error fetching contextual data:', error)
      const fallbackData = {
        user_role: 'customer',
        has_active_orders: false,
        recent_conversations: false
      }
      console.log('🔄 Using fallback data:', fallbackData)
      return fallbackData
    }
  }

  const getUserRole = async (user: User): Promise<'customer' | 'business' | 'driver'> => {
    try {
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${await user.getIdToken?.()}` // If using Firebase
        }
      })
      const data = await response.json()
      return data.role || 'customer'
    } catch (error) {
      console.error('Error fetching user role:', error)
      return 'customer' // Default fallback
    }
  }

  const checkActiveOrders = async (userId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/orders/active?user_id=${userId}`)
      const data = await response.json()
      return data.hasActiveOrders || false
    } catch (error) {
      console.error('Error checking active orders:', error)
      return false
    }
  }

  const handleActionClick = (action: EnhancedQuickAction) => {
    console.log('🎯 Template clicked:', action.title, action)
    console.log('🎯 Action type:', action.type, 'Action ID:', action.id)
    console.log('🎯 Checking conditions: type === "review_response"?', action.type === 'review_response', 'id === "respond-reviews"?', action.id === 'respond-reviews')

    // Handle "Respond to reviews" action specially
    if (action.type === 'review_response' && action.id === 'respond-reviews') {
      console.log('💭 ✅ MATCHED! Opening review response composer')

      // Get the first managed business for now - could be enhanced to let user select
      const managedBusinessNames = action.context?.managedBusinesses || []
      const managedBusinessIds = action.actionData?.managed_businesses || []

      if (managedBusinessNames.length > 0 && managedBusinessIds.length > 0) {
        const businessId = managedBusinessIds[0]
        const businessName = managedBusinessNames[0]

        console.log('💭 Setting business:', { businessId, businessName })
        setSelectedBusinessId(businessId.toString())
        setSelectedBusinessName(businessName)
        setShowReviewResponseComposer(true)
      } else {
        console.error('No managed businesses found for review response', {
          managedBusinessNames,
          managedBusinessIds
        })
      }
      return
    }

    console.log('💭 ❌ NOT MATCHED - proceeding with other action handling')

    // Handle quick review actions - directly open review modal with pre-selected order
    if (action.context?.type === 'quick_review') {
      console.log('📝 Opening review modal for quick review')
      setReviewType(action.context.review_type)
      setPreSelectedOrder(action.context.order)
      setShowReviewModal(true)
      return
    }

    // Handle review templates specially - open the review selection modal
    if (action.template && ['order-review', 'business-review', 'driver-review'].includes(action.template.id)) {
      console.log('📝 Opening review modal for template:', action.template.id)
      const type = action.template.id.replace('-review', '') as 'order' | 'business' | 'driver'
      setReviewType(type)
      setPreSelectedOrder(null) // Clear pre-selected order for template-based reviews
      setShowReviewModal(true)
      return
    }

    if (action.type === 'template' && action.template) {
      console.log('📧 Opening template composer for:', action.template.title)
      onTemplateSelected(action.template, context)
    } else if (action.type === 'quick_response' && action.quick_response) {
      console.log('⚡ Quick response selected:', action.quick_response.text)
      onQuickResponseSelected(action.quick_response)
    } else {
      console.log('🔧 Custom action selected:', action.title)
      onCustomActionSelected(action)
    }
  }

  const handleReviewSubmitted = (reviewData: any) => {
    // Show success message or handle the review submission
    console.log('Review submitted:', reviewData)
    // You could show a toast notification here
  }

  const handleReviewResponseSent = (reviewId: number, response: string) => {
    console.log('Review response sent:', { reviewId, response })
    // Could refresh the reviews or show a success message
  }



  if (isLoading) {
    return (
      <div className="px-4 space-y-3">
        <div className="h-4 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-2 gap-2">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="h-20 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 space-y-4">
      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Zap className="h-4 w-4 text-emerald-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 gap-2">
            {actions.map((action) => (
              <Button
                key={action.id}
                variant="outline"
                className={cn(
                  "h-auto p-3 flex flex-col items-start text-left space-y-1 hover:bg-gray-50",
                  action.is_urgent && "border-red-200 bg-red-50/30"
                )}
                onClick={() => handleActionClick(action)}
              >
                <div className="flex items-center gap-2 w-full">
                  {action.icon}
                  {action.is_urgent && (
                    <Badge variant="destructive" className="text-xs px-1 py-0">
                      Urgent
                    </Badge>
                  )}
                </div>
                <div className="text-xs font-medium">{action.title}</div>
                {action.description && (
                  <div className="text-xs text-gray-500 line-clamp-2">
                    {action.description}
                  </div>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>



      {/* Review Selection Modal */}
      <ReviewSelectionModal
        isOpen={showReviewModal}
        onClose={() => {
          setShowReviewModal(false)
          setPreSelectedOrder(null) // Clear pre-selected order when closing
        }}
        reviewType={reviewType}
        user={user}
        preSelectedOrder={preSelectedOrder}
        onReviewSubmitted={(reviewData) => {
          console.log('Review submitted:', reviewData)
          setPreSelectedOrder(null) // Clear pre-selected order after submission
          handleReviewSubmitted(reviewData)
        }}
      />

      {/* Review Response Composer */}
      <ReviewResponseComposer
        isOpen={showReviewResponseComposer}
        onClose={() => setShowReviewResponseComposer(false)}
        businessId={selectedBusinessId}
        businessName={selectedBusinessName}
        onResponseSent={handleReviewResponseSent}
      />
    </div>
  )
}
