"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Send,
  X,
  Star,
  MessageSquare,
  Calendar,
  User,
  Lock,
  CheckCircle,
  Loader2,
  Search
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from '@/components/ui/use-toast'
import { addAuthHeaders } from '@/utils/auth-token'

interface Review {
  id: number
  rating: number
  comment: string | null
  created_at: string
  order_id: number
  user_id: string
  businesses?: {
    name: string
  }
  orders?: {
    order_number: string
  }
  business_reply?: {
    content: string
    created_at: string
  }
}

interface Business {
  id: number
  name: string
  display_name?: string
}

interface ReviewResponseComposerProps {
  isOpen: boolean
  onClose: () => void
  businessId: number
  businessName: string
  managedBusinesses?: Business[]
  onResponseSent: (reviewId: number, response: string) => void
}

export function ReviewResponseComposer({
  isOpen,
  onClose,
  businessId,
  businessName,
  managedBusinesses = [],
  onResponseSent
}: ReviewResponseComposerProps) {
  const [reviews, setReviews] = useState<Review[]>([])
  const [selectedReviewId, setSelectedReviewId] = useState<string>('')
  const [responseContent, setResponseContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentBusinessId, setCurrentBusinessId] = useState<number>(businessId)
  const [currentBusinessName, setCurrentBusinessName] = useState<string>(businessName)
  const { toast } = useToast()

  useEffect(() => {
    if (isOpen) {
      fetchReviews()
    }
  }, [isOpen, currentBusinessId])

  const handleBusinessChange = (newBusinessId: string) => {
    const businessId = parseInt(newBusinessId)
    const business = managedBusinesses.find(b => b.id === businessId)
    if (business) {
      setCurrentBusinessId(businessId)
      setCurrentBusinessName(business.name || business.display_name || 'Business')
      setSelectedReviewId('') // Reset selected review when changing business
      setResponseContent('') // Reset response content
    }
  }

  const fetchReviews = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/reviews?business_id=${currentBusinessId}&include_replies=true&limit=50`)

      if (!response.ok) {
        throw new Error('Failed to fetch reviews')
      }

      const data = await response.json()

      if (data.success) {
        // Filter to show only reviews without replies or recent reviews
        const reviewsWithoutReplies = (data.reviews || []).filter((review: Review) =>
          !review.business_reply
        )
        setReviews(reviewsWithoutReplies)
      }
    } catch (err) {
      console.error('Error fetching reviews:', err)
      toast({
        title: "Error",
        description: "Failed to load reviews",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    if (!selectedReviewId || !responseContent.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select a review and enter your response.",
        variant: "destructive"
      })
      return
    }

    const selectedReview = reviews.find(r => r.id.toString() === selectedReviewId)
    if (!selectedReview) {
      toast({
        title: "Error",
        description: "Selected review not found.",
        variant: "destructive"
      })
      return
    }

    try {
      setSubmitting(true)

      // Prepare message data for review reply
      const messageData = {
        recipient_id: selectedReview.user_id,
        content: responseContent.trim(),
        subject: `Reply to your review of ${currentBusinessName}`,
        channel_type: 'review_reply',
        message_type: 'response',
        business_id: currentBusinessId,
        order_id: selectedReview.order_id,
        review_id: parseInt(selectedReviewId)
      }

      const response = await fetch('/api/connections-hub/messages', {
        method: 'POST',
        headers: addAuthHeaders(),
        body: JSON.stringify(messageData)
      })

      if (!response.ok) {
        throw new Error('Failed to send response')
      }

      const result = await response.json()

      toast({
        title: "Response Sent",
        description: "Your response has been sent to the customer and will appear on your business page.",
        variant: "default"
      })

      onResponseSent(parseInt(selectedReviewId), responseContent.trim())
      
      // Reset form
      setSelectedReviewId('')
      setResponseContent('')
      onClose()

    } catch (error) {
      console.error('Error sending review response:', error)
      toast({
        title: "Error",
        description: "Failed to send response. Please try again.",
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-4 w-4",
          i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
        )}
      />
    ))
  }

  const filteredReviews = reviews.filter(review => 
    !searchQuery || 
    review.comment?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    review.orders?.order_number?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const selectedReview = reviews.find(r => r.id.toString() === selectedReviewId)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2 text-emerald-600" />
            Respond to reviews
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Business Selector (if multiple businesses) */}
          {managedBusinesses.length > 1 && (
            <div className="space-y-2">
              <Label htmlFor="business-select">Select business:</Label>
              <Select value={currentBusinessId.toString()} onValueChange={handleBusinessChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a business..." />
                </SelectTrigger>
                <SelectContent>
                  {managedBusinesses.map((business) => (
                    <SelectItem key={business.id} value={business.id.toString()}>
                      {business.name || business.display_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Privacy Notice */}
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Lock className="h-4 w-4 text-gray-600" />
            <div className="text-sm text-gray-700">
              <span className="font-medium">Private conversation</span>
              <p className="text-xs text-gray-600 mt-1">
                This message will only be visible to you and the recipient.
              </p>
            </div>
          </div>

          {/* Review Selection */}
          <div className="space-y-2">
            <Label htmlFor="review-select">Select a review to respond to:</Label>
            {loading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
                <span className="ml-2 text-sm text-gray-600">Loading reviews...</span>
              </div>
            ) : (
              <Select value={selectedReviewId} onValueChange={setSelectedReviewId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a review to respond to..." />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {filteredReviews.length === 0 ? (
                    <div className="p-4 text-center text-sm text-gray-500">
                      No reviews available to respond to
                    </div>
                  ) : (
                    filteredReviews.map((review) => (
                      <SelectItem key={review.id} value={review.id.toString()}>
                        <div className="flex items-center space-x-2 py-1">
                          <div className="flex">
                            {renderStars(review.rating)}
                          </div>
                          <span className="text-xs text-gray-500">
                            {formatDate(review.created_at)}
                          </span>
                          <span className="text-sm truncate max-w-48">
                            {review.comment || 'No comment'}
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Selected Review Preview */}
          {selectedReview && (
            <Card className="bg-gray-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className="flex">
                      {renderStars(selectedReview.rating)}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Order #{selectedReview.orders?.order_number || selectedReview.order_id}
                    </Badge>
                  </div>
                  <span className="text-xs text-gray-500">
                    {formatDate(selectedReview.created_at)}
                  </span>
                </div>
                {selectedReview.comment && (
                  <p className="text-sm text-gray-700 italic">
                    "{selectedReview.comment}"
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {/* Response Content */}
          <div className="space-y-2">
            <Label htmlFor="response">Your response:</Label>
            <Textarea
              id="response"
              placeholder="Engage with customer feedback"
              value={responseContent}
              onChange={(e) => setResponseContent(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Message Preview */}
          {responseContent.trim() && (
            <div className="p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
                <span className="text-sm font-medium text-emerald-800">Message ready to send</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose} disabled={submitting}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={!selectedReviewId || !responseContent.trim() || submitting}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Message
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
