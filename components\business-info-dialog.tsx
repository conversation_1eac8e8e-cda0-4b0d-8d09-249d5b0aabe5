import React from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Clock, 
  MapPin, 
  Phone, 
  Mail, 
  Star, 
  AlertTriangle, 
  Calendar
} from 'lucide-react';
import { Restaurant } from '@/types/business';
import { formatOpeningHours } from '@/lib/date-utils';

interface BusinessInfoDialogProps {
  business: Restaurant;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const BusinessInfoDialog: React.FC<BusinessInfoDialogProps> = ({
  business,
  open,
  onOpenChange
}) => {
  // Format opening hours for display
  const formattedOpeningHours = business.opening_hours 
    ? Object.entries(business.opening_hours).map(([day, hours]) => ({
        day: day.charAt(0).toUpperCase() + day.slice(1),
        hours: hours.open && hours.close ? `${hours.open} - ${hours.close}` : 'Closed'
      }))
    : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            {business.name} Information
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Description */}
          {business.description && (
            <div>
              <h3 className="font-semibold text-lg mb-2">About</h3>
              <p className="text-sm text-gray-700">{business.description}</p>
            </div>
          )}

          {/* Address & Contact */}
          <div>
            <h3 className="font-semibold text-lg mb-2">Contact Information</h3>
            <div className="space-y-3">
              {business.address && (
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Address</p>
                    <p className="text-sm text-gray-600">{business.address}</p>
                    <p className="text-sm text-gray-600">{business.location}, Jersey</p>
                  </div>
                </div>
              )}
              
              {business.phone && (
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <p className="text-sm text-gray-600">{business.phone}</p>
                  </div>
                </div>
              )}
              
              {business.email && (
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-gray-600">{business.email}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Opening Hours */}
          {formattedOpeningHours.length > 0 && (
            <div>
              <h3 className="font-semibold text-lg mb-2">Opening Hours</h3>
              <div className="grid grid-cols-2 gap-2">
                {formattedOpeningHours.map((item) => (
                  <div key={item.day} className="flex justify-between">
                    <span className="text-sm font-medium">{item.day}</span>
                    <span className="text-sm text-gray-600">{item.hours}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Hygiene Rating */}
          {business.hygiene_rating && (
            <div>
              <h3 className="font-semibold text-lg mb-2">Food Hygiene Rating</h3>
              <div className="flex items-center">
                <div className="bg-green-100 text-green-800 font-bold text-lg px-3 py-1 rounded-md mr-2">
                  {business.hygiene_rating}
                </div>
                <div>
                  <p className="text-sm text-gray-600">
                    Food hygiene rating
                  </p>
                  {business.last_inspection_date && (
                    <p className="text-xs text-gray-500 flex items-center mt-1">
                      <Calendar className="h-3 w-3 mr-1" />
                      Last inspected: {business.last_inspection_date}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Allergen Information */}
          <div>
            <h3 className="font-semibold text-lg mb-2">Allergen Information</h3>
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-gray-600">
                {Array.isArray(business.allergen_info) && business.allergen_info.length > 0
                  ? business.allergen_info.join(', ')
                  : typeof business.allergen_info === 'string' && business.allergen_info
                  ? business.allergen_info
                  : "Please contact the restaurant directly for detailed allergen information. All dishes may contain traces of allergens."}
              </p>
            </div>
          </div>


        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BusinessInfoDialog;
